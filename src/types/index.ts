export interface LoginRequest {
  grant_type: string;
  username: string;
  password: string;
  scope: string;
  client_id: string;
}

export interface LoginResponse {
  id: string;
  access_token: string;
  token_type: string;
  username: string;
  role: string;
  tenant_id: string;
  tenant_label: string;
  tenant_slug: string;
}

export interface GoogleApiKeyResponse {
  GOOGLE_API_KEY: string;
}

export interface Prompt {
  _id: string;
  name: string;
  text: string;
  model: string;
}

export interface PromptsResponse {
  prompts: Prompt[];
}

export interface Voice {
  _id: string;
  name: string;
}

export interface VoicesResponse {
  voices: Voice[];
}

export interface User {
  id: string;
  username: string;
  role: string;
  tenant_id: string;
  tenant_label: string;
}

export interface DecodedToken {
  sub: string;
  role: string;
  tenant_id: string;
  exp: number;
}

export interface TTSRequest {
  text: string;
  voiceId: string;
  promptText: string;
}