
interface TTSRequest {
  text: string;
  gender: string;
  speaker: number;
  length_scale: number;
  noise_scale: number;
  noise_w: number;
  sentence_silence: number;
  input_delay: number;
}

interface LocalTTSResponse {
  audio_data?: string; // base64 encoded audio (optional since API might return binary)
}

class LocalTTSService {
  private getAuthHeaders(): Record<string, string> {
    const token = localStorage.getItem('access_token');
    return {
      'Authorization': `Bear<PERSON> ${token}`,
      'Content-Type': 'application/json',
      'accept': 'application/json'
    };
  }

  async generateAudio(
    text: string,
    voiceName: string,
    systemPrompt: string,
    apiKey: string,
    speaker: number = 0,
    lengthScale: number = 1,
    noiseScale: number = 0.667,
    noiseW: number = 0.8,
    sentenceSilence: number = 0,
    inputDelay: number = 0
  ): Promise<Uint8Array> {
    try {
      console.log('Starting Local TTS generation');
      console.log('Voice name:', voiceName);
      console.log('Text length:', text.length);
      console.log('Parameters:', { speaker, lengthScale, noiseScale, noiseW, sentenceSilence, inputDelay });

      const response = await fetch('https://int-api.nextai.asia/v1/tts/local', {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          text: text,
          gender: voiceName,
          speaker: speaker,
          length_scale: lengthScale,
          noise_scale: noiseScale,
          noise_w: noiseW,
          sentence_silence: sentenceSilence,
          input_delay: inputDelay
        })
      });

      if (!response.ok) {
        throw new Error(`TTS API failed: ${response.status} - ${response.statusText}`);
      }

      // Check if response is JSON or binary audio
      const contentType = response.headers.get('content-type');
      console.log('Response content type:', contentType);

      if (contentType && contentType.includes('application/json')) {
        // Handle JSON response with base64 audio
        const data: LocalTTSResponse = await response.json();

        if (!data.audio_data) {
          throw new Error('No audio data received from TTS API');
        }

        // Convert base64 to Uint8Array
        const binaryString = atob(data.audio_data);
        const audioData = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          audioData[i] = binaryString.charCodeAt(i);
        }

        console.log('Audio generation completed (JSON), final size:', audioData.length);
        return audioData;
      } else {
        // Handle direct binary audio response
        const arrayBuffer = await response.arrayBuffer();
        const audioData = new Uint8Array(arrayBuffer);

        console.log('Audio generation completed (binary), final size:', audioData.length);
        return audioData;
      }
      
    } catch (error) {
      console.error('Audio generation failed:', error);

      // Provide more specific error messages
      if (error instanceof SyntaxError && error.message.includes('Unexpected token')) {
        throw new Error('API returned invalid response format. Expected JSON or binary audio data.');
      }

      throw error;
    }
  }
}

export const geminiService = new LocalTTSService();
