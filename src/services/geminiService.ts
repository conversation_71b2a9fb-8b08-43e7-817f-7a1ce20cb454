
interface TTSRequest {
  text: string;
  gender: string;
  speaker: number;
  length_scale: number;
  noise_scale: number;
  noise_w: number;
  sentence_silence: number;
  input_delay: number;
}

interface LocalTTSResponse {
  audio_data: string; // base64 encoded audio
}

class LocalTTSService {
  private getAuthHeaders(): Record<string, string> {
    const token = localStorage.getItem('access_token');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      'accept': 'application/json'
    };
  }

  async generateAudio(text: string, voiceName: string, systemPrompt: string, apiKey: string): Promise<Uint8Array> {
    try {
      console.log('Starting Local TTS generation');
      console.log('Voice name:', voiceName);
      console.log('Text length:', text.length);
      
      const response = await fetch('https://int-api.nextai.asia/v1/tts/local', {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          text: text,
          gender: voiceName,
          speaker: 0,
          length_scale: 1,
          noise_scale: 0.667,
          noise_w: 0.8,
          sentence_silence: 0,
          input_delay: 0
        })
      });

      if (!response.ok) {
        throw new Error(`TTS API failed: ${response.status} - ${response.statusText}`);
      }

      const data: LocalTTSResponse = await response.json();
      
      if (!data.audio_data) {
        throw new Error('No audio data received from TTS API');
      }

      // Convert base64 to Uint8Array
      const binaryString = atob(data.audio_data);
      const audioData = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        audioData[i] = binaryString.charCodeAt(i);
      }

      console.log('Audio generation completed, final size:', audioData.length);
      return audioData;
      
    } catch (error) {
      console.error('Audio generation failed:', error);
      throw error;
    }
  }
}

export const geminiService = new LocalTTSService();
