import { 
  LoginRequest, 
  LoginResponse, 
  GoogleApiKeyResponse, 
  PromptsResponse, 
  VoicesResponse 
} from '../types';

const BASE_URL = 'https://int-api.nextai.asia/v1';

class ApiService {
  private getAuthHeaders(): Record<string, string> {
    const token = localStorage.getItem('access_token');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
  }

  async login(credentials: Omit<LoginRequest, 'grant_type' | 'scope'>): Promise<LoginResponse> {
    console.log('Attempting login with:', { 
      username: credentials.username, 
      client_id: credentials.client_id,
      endpoint: `${BASE_URL}/login`
    });

    try {
      const response = await fetch(`${BASE_URL}/login`, {
        method: 'POST',
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'password',
          username: credentials.username,
          password: credentials.password,
          scope: '',
          client_id: credentials.client_id,
        }),
      });

      console.log('Login response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Login failed with response:', errorText);
        throw new Error(`Login failed: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('Login successful:', { id: data.id, username: data.username, role: data.role });
      return data;
    } catch (error) {
      console.error('Login request failed:', error);
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Network error: Unable to connect to API server. Please check your internet connection.');
      }
      throw error;
    }
  }

  async getGoogleApiKey(): Promise<GoogleApiKeyResponse> {
    try {
      const response = await fetch(`${BASE_URL}/configs/google-api-key`, {
        headers: this.getAuthHeaders(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get Google API key: ${response.status} - ${errorText}`);
      }

      return response.json();
    } catch (error) {
      console.error('Google API key request failed:', error);
      throw error;
    }
  }

  async getPrompts(): Promise<PromptsResponse> {
    const response = await fetch(`${BASE_URL}/prompts`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to get prompts');
    }

    return response.json();
  }

  async getVoices(): Promise<VoicesResponse> {
    const response = await fetch(`${BASE_URL}/voices`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to get voices');
    }

    return response.json();
  }
}

export const apiService = new ApiService();