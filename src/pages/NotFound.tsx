import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { Button } from "../components/ui/button";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="text-center space-y-6">
        <div className="text-center space-y-4">
          <img 
            src="/lovable-uploads/9fc3afea-5363-4774-b7f9-952c538415de.png" 
            alt="Logo" 
            className="mx-auto h-16 w-16"
          />
          <div>
            <h1 className="text-4xl font-bold mb-4">404</h1>
            <p className="text-xl text-muted-foreground mb-4">Page not found</p>
            <p className="text-muted-foreground">The page you're looking for doesn't exist.</p>
          </div>
        </div>
        <Button asChild variant="accent">
          <a href="/">Return to Home</a>
        </Button>
      </div>
    </div>
  );
};

export default NotFound;
