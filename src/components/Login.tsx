import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { apiService } from '../services/api';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Label } from './ui/label';
import { useToast } from '../hooks/use-toast';
export const Login: React.FC = () => {
  const [credentials, setCredentials] = useState({
    username: '',
    password: '',
    client_id: 'apitest'
  });
  const [isLoading, setIsLoading] = useState(false);
  const {
    login
  } = useAuth();
  const {
    toast
  } = useToast();
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const response = await apiService.login(credentials);

      // Store the JWT token and user data
      login(response.access_token, {
        id: response.id,
        username: response.username,
        role: response.role,
        tenant_id: response.tenant_id,
        tenant_label: response.tenant_label
      });

      // Fetch and store Google API key
      try {
        const apiKeyResponse = await apiService.getGoogleApiKey();
        localStorage.setItem('google_api_key', apiKeyResponse.GOOGLE_API_KEY);
      } catch (error) {
        console.warn('Could not fetch Google API key:', error);
      }
      toast({
        title: "Login successful",
        description: "Welcome to Next AI Admin Panel"
      });
    } catch (error) {
      toast({
        title: "Login failed",
        description: "Please check your credentials and try again",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };
  return <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center space-y-4">
          <img src="/lovable-uploads/8e6827ae-e69c-48dd-a1cf-6fb983da0dec.png" alt="Next AI Logo" className="mx-auto h-16 w-auto" />
          <div>
            
            
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Sign In</CardTitle>
            
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input id="username" type="text" value={credentials.username} onChange={e => setCredentials(prev => ({
                ...prev,
                username: e.target.value
              }))} required className="h-12" />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input id="password" type="password" value={credentials.password} onChange={e => setCredentials(prev => ({
                ...prev,
                password: e.target.value
              }))} required className="h-12" />
              </div>
              
              <Button type="submit" className="w-full h-12" variant="accent" disabled={isLoading}>
                {isLoading ? 'Signing in...' : 'Sign In'}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>;
};