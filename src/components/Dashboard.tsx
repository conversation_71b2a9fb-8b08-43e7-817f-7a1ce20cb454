import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { apiService } from '../services/api';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Prompt, Voice } from '../types';
import { TTSGenerator } from './TTSGenerator';
import { useToast } from '../hooks/use-toast';
export const Dashboard: React.FC = () => {
  const {
    user,
    logout,
    isTokenExpired
  } = useAuth();
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [voices, setVoices] = useState<Voice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const {
    toast
  } = useToast();
  useEffect(() => {
    const checkTokenAndFetchData = async () => {
      if (isTokenExpired()) {
        toast({
          title: "Session expired",
          description: "Please log in again",
          variant: "destructive"
        });
        logout();
        return;
      }
      try {
        const [promptsResponse, voicesResponse] = await Promise.all([apiService.getPrompts(), apiService.getVoices()]);
        setPrompts(promptsResponse.prompts);
        setVoices(voicesResponse.voices);
      } catch (error) {
        console.error('Failed to fetch data:', error);
        toast({
          title: "Error",
          description: "Failed to fetch data from server",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };
    checkTokenAndFetchData();
  }, [isTokenExpired, logout, toast]);

  // Check token expiration every minute
  useEffect(() => {
    const interval = setInterval(() => {
      if (isTokenExpired()) {
        toast({
          title: "Session expired",
          description: "Please log in again",
          variant: "destructive"
        });
        logout();
      }
    }, 60000);
    return () => clearInterval(interval);
  }, [isTokenExpired, logout, toast]);
  if (isLoading) {
    return <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent mx-auto"></div>
          <p>Loading dashboard...</p>
        </div>
      </div>;
  }
  return <div className="min-h-screen bg-background">
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <img src="/lovable-uploads/9fc3afea-5363-4774-b7f9-952c538415de.png" alt="Logo" className="h-8 w-8" />
            <div>
              <h1 className="text-xl font-bold">Next AI Admin</h1>
              <p className="text-sm text-muted-foreground">Text-to-Speech Generator</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <span className="text-sm text-muted-foreground">
              Welcome, {user?.username}
            </span>
            <Button variant="outline" onClick={logout}>
              Sign Out
            </Button>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="">
          <Card>
            
            
          </Card>

          <TTSGenerator prompts={prompts} voices={voices} />
        </div>
      </main>
    </div>;
};