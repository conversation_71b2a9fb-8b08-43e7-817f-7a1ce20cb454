import React, { useState } from 'react';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Textarea } from './ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Label } from './ui/label';
import { Prompt, Voice } from '../types';
import { geminiService } from '../services/geminiService';
import { AudioPlayer } from './AudioPlayer';
import { useToast } from '../hooks/use-toast';

interface TTSGeneratorProps {
  prompts: Prompt[];
  voices: Voice[];
}

export const TTSGenerator: React.FC<TTSGeneratorProps> = ({ prompts, voices }) => {
  const [text, setText] = useState('');
  const [selectedPrompt, setSelectedPrompt] = useState<string>('');
  const [selectedVoice, setSelectedVoice] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [audioData, setAudioData] = useState<Uint8Array | null>(null);
  const [audioUrl, setAudioUrl] = useState<string>('');
  const { toast } = useToast();

  // Auto-select first prompt when prompts load
  React.useEffect(() => {
    if (prompts.length > 0 && !selectedPrompt) {
      setSelectedPrompt(prompts[0]._id);
    }
  }, [prompts, selectedPrompt]);

  const handleGenerate = async () => {
    if (!text.trim() || !selectedPrompt || !selectedVoice) {
      toast({
        title: "Missing information",
        description: "Please enter text and select a voice",
        variant: "destructive",
      });
      return;
    }

    const apiKey = localStorage.getItem('google_api_key');
    if (!apiKey) {
      toast({
        title: "API Key missing",
        description: "Google API key not found",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    
    try {
      const prompt = prompts.find(p => p._id === selectedPrompt);
      const voice = voices.find(v => v._id === selectedVoice);

      if (!prompt || !voice) {
        throw new Error('Selected prompt or voice not found');
      }

      toast({
        title: "Generating audio...",
        description: "This may take a moment. Please wait while we generate your speech.",
      });

      // Generate audio using Gemini TTS API
      const audioData = await geminiService.generateAudio(
        text,
        voice.name,
        prompt.text,
        apiKey
      );

      setAudioData(audioData);
      
      // Create a blob URL for audio playback
      const blob = new Blob([audioData], { type: 'audio/wav' });
      const url = URL.createObjectURL(blob);
      setAudioUrl(url);

      toast({
        title: "Audio generated successfully!",
        description: "Your text-to-speech audio is ready to play and download.",
      });
    } catch (error) {
      console.error('TTS generation failed:', error);
      
      let errorMessage = "Failed to generate audio. Please try again.";
      let errorDescription = "";
      
      if (error instanceof Error) {
        if (error.message.includes('Invalid Google API key')) {
          errorMessage = "API Key Configuration Error";
          errorDescription = "The Google API key appears to be invalid or not properly configured. Please contact your administrator to ensure a valid Gemini TTS API key is set up.";
        } else if (error.message.includes('No audio data received')) {
          errorMessage = "Audio Generation Failed";
          errorDescription = "No audio was generated. This might be due to voice compatibility or API limitations.";
        } else if (error.message.includes('fetch')) {
          errorMessage = "Network Error";
          errorDescription = "Unable to connect to Google's TTS service. Please check your internet connection.";
        } else {
          errorMessage = "Generation Error";
          errorDescription = error.message;
        }
      }
      
      toast({
        title: errorMessage,
        description: errorDescription,
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownload = () => {
    if (audioData) {
      const blob = new Blob([audioData], { type: 'audio/wav' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `tts-audio-${Date.now()}.wav`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Text-to-Speech Generator</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="voice-select">Select Voice</Label>
          <Select value={selectedVoice} onValueChange={setSelectedVoice}>
            <SelectTrigger>
              <SelectValue placeholder="Choose a voice..." />
            </SelectTrigger>
            <SelectContent>
              {voices.map((voice) => (
                <SelectItem key={voice._id} value={voice._id}>
                  {voice.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="text-input">Text to Convert</Label>
          <Textarea
            id="text-input"
            placeholder="Enter the text you want to convert to speech..."
            value={text}
            onChange={(e) => setText(e.target.value)}
            rows={6}
            className="resize-none"
          />
        </div>

        <div className="flex gap-4">
          <Button 
            onClick={handleGenerate}
            disabled={isGenerating || !text.trim() || !selectedPrompt || !selectedVoice}
            variant="accent"
            className="flex-1"
          >
            {isGenerating ? 'Generating Audio... Please wait' : 'Generate Audio'}
          </Button>
          
          {audioData && (
            <Button onClick={handleDownload} variant="outline">
              Download
            </Button>
          )}
        </div>

        {audioUrl && (
          <AudioPlayer audioUrl={audioUrl} />
        )}
      </CardContent>
    </Card>
  );
};