# ---- Base image ----
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Copy source files
COPY . .

# Install deps
RUN npm install

# Optional: delete old dist/ if present
RUN rm -rf dist || true

# Build the Vite app
RUN npm run build

# Install static file server
RUN npm install -g serve

# Expose port
ENV PORT=6742
EXPOSE 6742

# Serve the built app
CMD ["serve", "-s", "dist", "-l", "6742"]